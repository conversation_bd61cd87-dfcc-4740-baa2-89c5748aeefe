[{"session_id": "707ca1a3-4148-4e7a-b580-cc510bc29a34", "transcript_path": "/Users/<USER>/.claude/projects/-Users-mchorfa-Documents-augment-projects-mc-airgapper/707ca1a3-4148-4e7a-b580-cc510bc29a34.jsonl", "hook_event_name": "Notification", "message": "<PERSON> needs your permission to use Bash"}, {"session_id": "707ca1a3-4148-4e7a-b580-cc510bc29a34", "transcript_path": "/Users/<USER>/.claude/projects/-Users-mchorfa-Documents-augment-projects-mc-airgapper/707ca1a3-4148-4e7a-b580-cc510bc29a34.jsonl", "hook_event_name": "Notification", "message": "<PERSON> needs your permission to use Write"}, {"session_id": "707ca1a3-4148-4e7a-b580-cc510bc29a34", "transcript_path": "/Users/<USER>/.claude/projects/-Users-mchorfa-Documents-augment-projects-mc-airgapper/707ca1a3-4148-4e7a-b580-cc510bc29a34.jsonl", "hook_event_name": "Notification", "message": "<PERSON> is waiting for your input"}]
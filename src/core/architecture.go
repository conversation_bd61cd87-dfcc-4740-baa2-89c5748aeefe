// Package: core
// Agent: AG-013 (<PERSON>)
// GAL: 3
// Coordinating Agents: [AG-001, AG-005, AG-006]
// URN: urn:mc:exec:tenant:claude-code:architecture:2025-01-17T10:00:00.000Z
// 
// Purpose: Five-plane distributed system architecture for air-gapped artifact management
// Governance: EU AI Act, NIST 800-53, ISO 27001 compliant
// Security: Zero-trust, cryptographic provenance, quantum-resistant design
package core

import (
	"context"
	"github.com/mcstack/governance"
	"github.com/mcstack/telemetry"
)

// PlaneType defines the architectural planes in the system
type PlaneType string

const (
	ControlPlane      PlaneType = "control"
	DataPlane         PlaneType = "data"
	ManagementPlane   PlaneType = "management"
	ObservabilityPlane PlaneType = "observability"
	SecurityPlane     PlaneType = "security"
)

// AirgapperArchitecture represents the core system architecture
type AirgapperArchitecture struct {
	planes       map[PlaneType]Plane
	governance   governance.Engine
	telemetry    telemetry.Collector
	securityMode SecurityMode
}

// Plane represents an architectural plane in the system
type Plane interface {
	Initialize(ctx context.Context, config PlaneConfig) error
	Start(ctx context.Context) error
	Stop(ctx context.Context) error
	Health() HealthStatus
	Metrics() PlaneMetrics
}

// PlaneConfig contains configuration for each plane
type PlaneConfig struct {
	Name           string
	Type           PlaneType
	GALLevel       int
	Constraints    governance.Policy
	SecurityConfig SecurityConfig
}

// SecurityMode defines the security posture of the system
type SecurityMode string

const (
	SecurityModeAirgapped    SecurityMode = "airgapped"
	SecurityModeHybrid       SecurityMode = "hybrid"
	SecurityModeConnected    SecurityMode = "connected"
)

// SecurityConfig contains security configuration
type SecurityConfig struct {
	Mode                SecurityMode
	CryptographicSuite  CryptoSuite
	AttestationLevel    AttestationLevel
	QuantumResistant    bool
	HSMEnabled          bool
}

// CryptoSuite defines the cryptographic algorithms in use
type CryptoSuite struct {
	SignatureAlgorithm   string
	HashAlgorithm        string
	EncryptionAlgorithm  string
	KeyDerivation        string
}

// AttestationLevel defines SLSA attestation requirements
type AttestationLevel int

const (
	AttestationLevel0 AttestationLevel = iota
	AttestationLevel1
	AttestationLevel2
	AttestationLevel3
	AttestationLevel4
)

// HealthStatus represents the health of a plane
type HealthStatus struct {
	Healthy       bool
	LastCheck     int64
	ErrorCount    int
	Degradations  []string
}

// PlaneMetrics contains metrics for a plane
type PlaneMetrics struct {
	RequestCount      int64
	ErrorCount        int64
	AvgResponseTimeMs float64
	Throughput        float64
}

// NewArchitecture creates a new airgapper architecture
func NewArchitecture(ctx context.Context, config ArchitectureConfig) (*AirgapperArchitecture, error) {
	arch := &AirgapperArchitecture{
		planes:       make(map[PlaneType]Plane),
		governance:   config.GovernanceEngine,
		telemetry:    config.TelemetryCollector,
		securityMode: config.SecurityMode,
	}
	
	// Initialize all planes
	if err := arch.initializePlanes(ctx, config); err != nil {
		return nil, err
	}
	
	return arch, nil
}

// ArchitectureConfig contains configuration for the architecture
type ArchitectureConfig struct {
	SecurityMode       SecurityMode
	GovernanceEngine   governance.Engine
	TelemetryCollector telemetry.Collector
	PlaneConfigs       map[PlaneType]PlaneConfig
}

// initializePlanes initializes all architectural planes
func (a *AirgapperArchitecture) initializePlanes(ctx context.Context, config ArchitectureConfig) error {
	// Control Plane
	controlPlane := NewControlPlane()
	if err := controlPlane.Initialize(ctx, config.PlaneConfigs[ControlPlane]); err != nil {
		return err
	}
	a.planes[ControlPlane] = controlPlane
	
	// Data Plane
	dataPlane := NewDataPlane()
	if err := dataPlane.Initialize(ctx, config.PlaneConfigs[DataPlane]); err != nil {
		return err
	}
	a.planes[DataPlane] = dataPlane
	
	// Management Plane
	mgmtPlane := NewManagementPlane()
	if err := mgmtPlane.Initialize(ctx, config.PlaneConfigs[ManagementPlane]); err != nil {
		return err
	}
	a.planes[ManagementPlane] = mgmtPlane
	
	// Observability Plane
	obsPlane := NewObservabilityPlane()
	if err := obsPlane.Initialize(ctx, config.PlaneConfigs[ObservabilityPlane]); err != nil {
		return err
	}
	a.planes[ObservabilityPlane] = obsPlane
	
	// Security Plane
	secPlane := NewSecurityPlane()
	if err := secPlane.Initialize(ctx, config.PlaneConfigs[SecurityPlane]); err != nil {
		return err
	}
	a.planes[SecurityPlane] = secPlane
	
	return nil
}

// Start starts all planes
func (a *AirgapperArchitecture) Start(ctx context.Context) error {
	for planeType, plane := range a.planes {
		if err := plane.Start(ctx); err != nil {
			return err
		}
		a.telemetry.Record("plane_started", map[string]interface{}{
			"plane_type": planeType,
			"timestamp":  ctx.Value("timestamp"),
		})
	}
	return nil
}

// Stop stops all planes
func (a *AirgapperArchitecture) Stop(ctx context.Context) error {
	for planeType, plane := range a.planes {
		if err := plane.Stop(ctx); err != nil {
			return err
		}
		a.telemetry.Record("plane_stopped", map[string]interface{}{
			"plane_type": planeType,
			"timestamp":  ctx.Value("timestamp"),
		})
	}
	return nil
}

// GetPlane returns a specific plane
func (a *AirgapperArchitecture) GetPlane(planeType PlaneType) (Plane, bool) {
	plane, exists := a.planes[planeType]
	return plane, exists
}

// HealthCheck performs health check on all planes
func (a *AirgapperArchitecture) HealthCheck(ctx context.Context) map[PlaneType]HealthStatus {
	results := make(map[PlaneType]HealthStatus)
	for planeType, plane := range a.planes {
		results[planeType] = plane.Health()
	}
	return results
}

// Metrics returns metrics for all planes
func (a *AirgapperArchitecture) Metrics(ctx context.Context) map[PlaneType]PlaneMetrics {
	results := make(map[PlaneType]PlaneMetrics)
	for planeType, plane := range a.planes {
		results[planeType] = plane.Metrics()
	}
	return results
}
{"hooks": {"PreToolUse": [{"matcher": "", "hooks": [{"type": "command", "command": "uv run .claude/hooks/pre_tool_use.py"}, {"type": "command", "command": "uv run .claude/hooks/send_event.py --source-app cc-hook-multi-agent-obvs --event-type PreToolUse --summarize"}]}], "PostToolUse": [{"matcher": "", "hooks": [{"type": "command", "command": "uv run .claude/hooks/post_tool_use.py"}, {"type": "command", "command": "uv run .claude/hooks/send_event.py --source-app cc-hook-multi-agent-obvs --event-type PostToolUse --summarize"}]}], "Notification": [{"matcher": "", "hooks": [{"type": "command", "command": "uv run .claude/hooks/notification.py --notify "}, {"type": "command", "command": "uv run .claude/hooks/send_event.py --source-app cc-hook-multi-agent-obvs --event-type Notification --summarize"}]}], "Stop": [{"matcher": "", "hooks": [{"type": "command", "command": "uv run .claude/hooks/stop.py --chat"}, {"type": "command", "command": "uv run .claude/hooks/send_event.py --source-app cc-hook-multi-agent-obvs --event-type Stop --add-chat"}]}], "SubagentStop": [{"matcher": "", "hooks": [{"type": "command", "command": "uv run .claude/hooks/subagent_stop.py"}, {"type": "command", "command": "uv run .claude/hooks/send_event.py --source-app cc-hook-multi-agent-obvs --event-type SubagentStop"}]}], "PreCompact": [{"matcher": "", "hooks": [{"type": "command", "command": "uv run .claude/hooks/send_event.py --source-app cc-hook-multi-agent-obvs --event-type PreCompact"}]}]}}